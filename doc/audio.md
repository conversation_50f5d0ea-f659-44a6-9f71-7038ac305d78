# 间歇流音频系统设计文档

## 音频文件规划

### 文件格式统一
为了确保跨平台兼容性和音质一致性，建议统一使用以下格式：
- **推荐格式**: `.wav` (无损，兼容性好，音质最佳)
- **备选格式**: `.mp3` (压缩格式，文件较小)
- **统一标准**: 所有音频文件统一使用 `.wav` 格式，采样率 44.1kHz，16位深度
- **文件大小**: 单个音效文件控制在 50KB-200KB 之间

### 目录结构设计

```
assets/
└── audio/
    ├── themes/                    # 主题音效包
    │   ├── nature/               # 自然音主题
    │   │   ├── break_start.wav   # 小休息开始
    │   │   ├── break_end.wav     # 小休息结束
    │   │   └── session_complete.wav # 专注完成
    │   ├── synthetic/            # 合成音主题
    │   │   ├── break_start.wav
    │   │   ├── break_end.wav
    │   │   └── session_complete.wav
    │   ├── asmr/                 # ASMR主题
    │   │   ├── break_start.wav
    │   │   ├── break_end.wav
    │   │   └── session_complete.wav
    │   └── custom/               # 用户自定义
    │       ├── break_start.wav
    │       ├── break_end.wav
    │       └── session_complete.wav
    └── individual/               # 独立音效文件
        ├── nature/
        │   ├── water_drop.wav    # 水滴声
        │   ├── wind_chime.wav    # 风铃声
        │   ├── forest_birds.wav  # 森林鸟鸣
        │   ├── ocean_waves.wav   # 海浪声
        │   └── rain_drops.wav    # 雨声
        ├── synthetic/
        │   ├── soft_beep.wav     # 柔和提示音
        │   ├── clear_chime.wav   # 清脆提示音
        │   ├── gentle_tone.wav   # 温和提示音
        │   └── minimal_ping.wav  # 简约提示音
        └── asmr/
            ├── whisper.wav       # 轻柔耳语
            ├── paper_flip.wav    # 纸张翻动
            ├── keyboard_tap.wav  # 键盘敲击
            └── pen_writing.wav   # 笔尖书写
```

### 音频配置系统

#### 1. 主题模式配置
```json
{
  "theme_mode": true,
  "current_theme": "nature",
  "themes": {
    "nature": {
      "name": "自然音",
      "break_start": "themes/nature/break_start.wav",
      "break_end": "themes/nature/break_end.wav",
      "session_complete": "themes/nature/session_complete.wav"
    },
    "synthetic": {
      "name": "合成音",
      "break_start": "themes/synthetic/break_start.wav",
      "break_end": "themes/synthetic/break_end.wav",
      "session_complete": "themes/synthetic/session_complete.wav"
    }
  }
}
```

#### 2. 独立音效模式配置
```json
{
  "theme_mode": false,
  "individual_sounds": {
    "break_start": "individual/nature/water_drop.wav",
    "break_end": "individual/nature/wind_chime.wav",
    "session_complete": "individual/nature/forest_birds.wav"
  }
}
```

### 音频文件命名规范

#### 标准命名
- `break_start.wav` - 小休息开始提示音
- `break_end.wav` - 小休息结束提示音
- `session_complete.wav` - 专注完成提示音

#### 描述性命名（独立音效）
- 自然音: `water_drop.wav`, `wind_chime.wav`, `forest_birds.wav`
- 合成音: `soft_beep.wav`, `clear_chime.wav`, `gentle_tone.wav`
- ASMR: `whisper.wav`, `paper_flip.wav`, `keyboard_tap.wav`

### 音频属性要求

#### 技术规格
- **格式**: WAV (推荐) 或 MP3
- **采样率**: 44.1kHz
- **位深度**: 16-bit
- **声道**: 单声道或立体声
- **时长**: 1-3秒（提示音），3-5秒（完成音）

#### 音量规范
- **峰值音量**: -6dB 到 -3dB
- **平均音量**: -12dB 到 -9dB
- **动态范围**: 适中，避免过于突兀

### 扩展性设计

#### 用户自定义音效
1. 支持用户导入自定义音频文件
2. 自动转换格式和音量标准化
3. 提供音效预览功能
4. 支持音效包导入/导出

#### 音效包管理
1. 支持在线下载音效包
2. 音效包版本管理
3. 音效包分享功能
4. 社区音效包推荐

### 实现优先级

#### Phase 1 (当前实现)
- [x] 基础音频播放功能
- [x] 主题音效包支持
- [ ] 三个基础音效文件集成

#### Phase 2 (后续扩展)
- [ ] 独立音效选择
- [ ] 音量调节
- [ ] 音效预览
- [ ] 自定义音效导入

#### Phase 3 (高级功能)
- [ ] 在线音效包
- [ ] 音效包分享
- [ ] 音效可视化
- [ ] 音效淡入淡出

### 当前文件映射

根据您提供的文件，建议映射如下：
```
snap.wav → assets/audio/themes/nature/break_start.wav
snap-end.wav → assets/audio/themes/nature/break_end.wav
break.mp3 → assets/audio/themes/nature/session_complete.wav (建议转换为.wav)
```

**注意**: 建议将 `break.mp3` 转换为 `break.wav` 以保持格式统一性。

### 配置文件更新

需要更新 `pubspec.yaml` 添加音频资源：
```yaml
flutter:
  assets:
    - assets/audio/themes/
    - assets/audio/individual/
```

### 代码集成点

1. **AudioService**: 音频播放服务
2. **AppSettings**: 音频配置管理
3. **SettingsScreen**: 音频设置界面
4. **FocusScreen**: 音频触发逻辑

---

*本文档将随着音频系统的开发持续更新*
