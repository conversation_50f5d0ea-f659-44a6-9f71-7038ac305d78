import 'package:flutter/material.dart';
import '../utils/constants.dart';

// 自定义底部导航栏
class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: const Color(AppConstants.cardColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildNavItem(
            context,
            index: 0,
            icon: Icons.home_outlined,
            activeIcon: Icons.home,
            label: '主页',
          ),
          _buildNavItem(
            context,
            index: 1,
            icon: Icons.bar_chart_outlined,
            activeIcon: Icons.bar_chart,
            label: '统计',
          ),
          _buildNavItem(
            context,
            index: 2,
            icon: Icons.settings_outlined,
            activeIcon: Icons.settings,
            label: '设置',
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context, {
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
  }) {
    final isActive = currentIndex == index;
    
    return Expanded(
      child: GestureDetector(
        onTap: () => onTap(index),
        behavior: HitTestBehavior.opaque,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: AppConstants.animationDurationMs),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isActive 
                      ? const Color(AppConstants.primaryColor).withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                ),
                child: Icon(
                  isActive ? activeIcon : icon,
                  color: isActive 
                      ? const Color(AppConstants.primaryColor)
                      : const Color(AppConstants.textSecondaryColor),
                  size: 24,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                  color: isActive 
                      ? const Color(AppConstants.primaryColor)
                      : const Color(AppConstants.textSecondaryColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
