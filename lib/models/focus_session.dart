import '../utils/constants.dart';

// 专注会话数据模型
class FocusSession {
  final String id;
  final DateTime startTime;
  DateTime? endTime;
  final int durationMinutes;
  FocusSessionState state;
  int elapsedMinutes;
  int elapsedSeconds;
  List<BreakRecord> breaks;
  bool isCompleted;

  FocusSession({
    required this.id,
    required this.startTime,
    this.endTime,
    this.durationMinutes = AppConstants.defaultFocusDurationMinutes,
    this.state = FocusSessionState.notStarted,
    this.elapsedMinutes = 0,
    this.elapsedSeconds = 0,
    List<BreakRecord>? breaks,
    this.isCompleted = false,
  }) : breaks = breaks ?? [];

  // 计算剩余时间（秒）
  int get remainingSeconds {
    final totalSeconds = durationMinutes * 60;
    final elapsedTotalSeconds = elapsedMinutes * 60 + elapsedSeconds;
    return totalSeconds - elapsedTotalSeconds;
  }

  // 计算剩余分钟数
  int get remainingMinutes => remainingSeconds ~/ 60;

  // 计算剩余秒数（不包含分钟）
  int get remainingSecondsOnly => remainingSeconds % 60;

  // 计算完成百分比
  double get completionPercentage {
    final totalSeconds = durationMinutes * 60;
    final elapsedTotalSeconds = elapsedMinutes * 60 + elapsedSeconds;
    return elapsedTotalSeconds / totalSeconds;
  }

  // 获取总休息次数
  int get totalBreaks => breaks.length;

  // 获取总休息时长（秒）
  int get totalBreakDuration {
    return breaks.fold(0, (sum, breakRecord) => sum + breakRecord.durationSeconds);
  }

  // 获取平均休息间隔（分钟）
  double get averageBreakInterval {
    if (breaks.isEmpty) return 0.0;
    
    final totalElapsedMinutes = elapsedMinutes + (elapsedSeconds / 60);
    return totalElapsedMinutes / breaks.length;
  }

  // 开始会话
  void start() {
    state = FocusSessionState.active;
  }

  // 暂停会话
  void pause() {
    state = FocusSessionState.paused;
  }

  // 恢复会话
  void resume() {
    state = FocusSessionState.active;
  }

  // 完成会话
  void complete() {
    state = FocusSessionState.completed;
    endTime = DateTime.now();
    isCompleted = true;
  }

  // 取消会话
  void cancel() {
    state = FocusSessionState.cancelled;
    endTime = DateTime.now();
  }

  // 添加休息记录
  void addBreak(BreakRecord breakRecord) {
    breaks.add(breakRecord);
  }

  // 更新已用时间
  void updateElapsedTime(int minutes, int seconds) {
    elapsedMinutes = minutes;
    elapsedSeconds = seconds;
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'durationMinutes': durationMinutes,
      'state': state.index,
      'elapsedMinutes': elapsedMinutes,
      'elapsedSeconds': elapsedSeconds,
      'breaks': breaks.map((b) => b.toJson()).toList(),
      'isCompleted': isCompleted,
    };
  }

  // 从JSON创建
  factory FocusSession.fromJson(Map<String, dynamic> json) {
    return FocusSession(
      id: json['id'],
      startTime: DateTime.parse(json['startTime']),
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
      durationMinutes: json['durationMinutes'],
      state: FocusSessionState.values[json['state']],
      elapsedMinutes: json['elapsedMinutes'],
      elapsedSeconds: json['elapsedSeconds'],
      breaks: (json['breaks'] as List)
          .map((b) => BreakRecord.fromJson(b))
          .toList(),
      isCompleted: json['isCompleted'],
    );
  }
}

// 休息记录
class BreakRecord {
  final DateTime startTime;
  final int durationSeconds;
  final double reactionTimeSeconds;

  BreakRecord({
    required this.startTime,
    required this.durationSeconds,
    this.reactionTimeSeconds = 0.0,
  });

  Map<String, dynamic> toJson() {
    return {
      'startTime': startTime.toIso8601String(),
      'durationSeconds': durationSeconds,
      'reactionTimeSeconds': reactionTimeSeconds,
    };
  }

  factory BreakRecord.fromJson(Map<String, dynamic> json) {
    return BreakRecord(
      startTime: DateTime.parse(json['startTime']),
      durationSeconds: json['durationSeconds'],
      reactionTimeSeconds: json['reactionTimeSeconds'],
    );
  }
}
