// 应用常量定义
class AppConstants {
  // 时间相关常量
  static const int defaultFocusDurationMinutes = 90;
  static const int minBreakIntervalMinutes = 3;
  static const int maxBreakIntervalMinutes = 5;
  static const int minBreakDurationSeconds = 8;
  static const int maxBreakDurationSeconds = 10;
  static const int longBreakDurationMinutes = 20;

  // 颜色主题
  static const primaryColor = 0xFF6B73FF;
  static const secondaryColor = 0xFF9AA0FF;
  static const backgroundColor = 0xFFF8F9FA;
  static const cardColor = 0xFFFFFFFF;
  static const textPrimaryColor = 0xFF2D3748;
  static const textSecondaryColor = 0xFF718096;
  static const successColor = 0xFF48BB78;
  static const warningColor = 0xFFED8936;

  // 字体大小
  static const double titleFontSize = 24.0;
  static const double subtitleFontSize = 18.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;

  // 间距
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // 圆角
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;

  // 动画时长
  static const int animationDurationMs = 300;
  static const int pageTransitionDurationMs = 250;

  // 存储键名
  static const String keyFocusHistory = 'focus_history';
  static const String keySettings = 'app_settings';
  static const String keyStatistics = 'statistics_data';
  static const String keyAchievements = 'achievements';

  // 成就相关
  static const int firstSessionAchievement = 1;
  static const int threeDayStreakAchievement = 3;
  static const int hundredBreaksAchievement = 100;
  static const double highCompletionRate = 0.85;
  static const double mediumCompletionRate = 0.70;

  // 音频文件路径
  static const String breakStartSound = 'sounds/break_start.mp3';
  static const String breakEndSound = 'sounds/break_end.mp3';
  static const String sessionCompleteSound = 'sounds/session_complete.mp3';
}

// 应用状态枚举
enum AppState {
  idle,           // 空闲状态
  focusing,       // 专注中
  breaking,       // 小休息中
  completed,      // 专注完成
  longBreak,      // 长休息中
}

// 专注会话状态
enum FocusSessionState {
  notStarted,     // 未开始
  active,         // 进行中
  paused,         // 暂停
  breaking,       // 小休息
  completed,      // 已完成
  cancelled,      // 已取消
}

// 统计时间范围
enum StatisticsTimeRange {
  today,          // 今日
  week,           // 本周
  month,          // 本月
  year,           // 本年
}

// 成就类型
enum AchievementType {
  firstSession,   // 首次完成
  streak,         // 连续天数
  totalSessions,  // 总会话数
  completionRate, // 完成率
  totalBreaks,    // 总休息次数
}

// 提示音主题
enum SoundTheme {
  nature,         // 自然音
  synthetic,      // 合成音
  asmr,           // ASMR
  custom,         // 自定义
}
