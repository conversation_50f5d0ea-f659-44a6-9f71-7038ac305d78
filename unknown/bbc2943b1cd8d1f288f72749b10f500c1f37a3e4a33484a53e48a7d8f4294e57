---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/SwiftUICore.swiftmodule/arm64e-apple-macos.swiftmodule'
dependencies:
  - mtime:           1742265808000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.4/SwiftUICore.swiftmodule/arm64e-apple-macos.swiftmodule'
    size:            3707496
  - mtime:           1741406483000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1929664
    sdk_relative:    true
  - mtime:           1741407315000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1741411513000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1741403049000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1741753036000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1742085903000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1741410019000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1741748427000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1741407537000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3888
    sdk_relative:    true
  - mtime:           1741407553000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1063
    sdk_relative:    true
  - mtime:           1741407560000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1099
    sdk_relative:    true
  - mtime:           1741407563000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1100
    sdk_relative:    true
  - mtime:           1741407555000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1511
    sdk_relative:    true
  - mtime:           1741407568000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            851
    sdk_relative:    true
  - mtime:           1741407537000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            15282
    sdk_relative:    true
  - mtime:           1741406651000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            4262
    sdk_relative:    true
  - mtime:           1741407582000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            18253
    sdk_relative:    true
  - mtime:           1741408093000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            230631
    sdk_relative:    true
  - mtime:           1741408144000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22908
    sdk_relative:    true
  - mtime:           1741408692000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            167832
    sdk_relative:    true
  - mtime:           1741408664000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            6595
    sdk_relative:    true
  - mtime:           1741408850000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            57168
    sdk_relative:    true
  - mtime:           1741409051000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22857
    sdk_relative:    true
  - mtime:           1741409002000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            33307
    sdk_relative:    true
  - mtime:           1741409206000000000
    path:            'usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3649
    sdk_relative:    true
  - mtime:           1741408100000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3489
    sdk_relative:    true
  - mtime:           1741408729000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            95502
    sdk_relative:    true
  - mtime:           1741752981000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            995833
    sdk_relative:    true
  - mtime:           1741753119000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            53546
    sdk_relative:    true
  - mtime:           1741411588000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21316
    sdk_relative:    true
  - mtime:           1741412488000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1741411284000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            20608
    sdk_relative:    true
  - mtime:           1741407315000000000
    path:            'usr/include/os.apinotes'
    size:            1658
    sdk_relative:    true
  - mtime:           1741409034000000000
    path:            'usr/lib/swift/os.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            108063
    sdk_relative:    true
  - mtime:           1741413403000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22069
    sdk_relative:    true
  - mtime:           1741411557000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            10918
    sdk_relative:    true
  - mtime:           1741412492000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1738796627000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            80245
    sdk_relative:    true
  - mtime:           1742180978000000000
    path:            'System/Library/Frameworks/ApplicationServices.framework/Headers/ApplicationServices.apinotes'
    size:            2012
    sdk_relative:    true
  - mtime:           1742001086000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1741410325000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            25175
    sdk_relative:    true
  - mtime:           1741412555000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1580
    sdk_relative:    true
  - mtime:           1741411783000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1810
    sdk_relative:    true
  - mtime:           1741412206000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21594
    sdk_relative:    true
  - mtime:           1741408694000000000
    path:            'usr/lib/swift/simd.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            229503
    sdk_relative:    true
  - mtime:           1741413090000000000
    path:            'System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1006824
    sdk_relative:    true
version:         1
...
