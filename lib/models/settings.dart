import '../utils/constants.dart';

// 应用设置模型
class AppSettings {
  // 专注时间参数
  int focusDurationMinutes;
  bool useRandomBreakInterval;
  int fixedBreakIntervalMinutes;
  int minBreakIntervalMinutes;
  int maxBreakIntervalMinutes;
  bool useRandomBreakDuration;
  int fixedBreakDurationSeconds;
  int minBreakDurationSeconds;
  int maxBreakDurationSeconds;

  // 提示音设置
  SoundTheme soundTheme;
  String breakStartSound;
  String breakEndSound;
  String sessionCompleteSound;
  double soundVolume;
  bool enableVibration;

  // 界面设置
  bool enableDarkMode;
  String language;
  bool showSeconds;
  bool enableNotifications;

  // 目标设置
  int dailyFocusGoalMinutes;
  int weeklyFocusGoalMinutes;
  bool enableGoalReminders;

  AppSettings({
    this.focusDurationMinutes = AppConstants.defaultFocusDurationMinutes,
    this.useRandomBreakInterval = true,
    this.fixedBreakIntervalMinutes = 4,
    this.minBreakIntervalMinutes = AppConstants.minBreakIntervalMinutes,
    this.maxBreakIntervalMinutes = AppConstants.maxBreakIntervalMinutes,
    this.useRandomBreakDuration = true,
    this.fixedBreakDurationSeconds = 8,
    this.minBreakDurationSeconds = AppConstants.minBreakDurationSeconds,
    this.maxBreakDurationSeconds = AppConstants.maxBreakDurationSeconds,
    this.soundTheme = SoundTheme.nature,
    this.breakStartSound = '水滴声',
    this.breakEndSound = '风铃声',
    this.sessionCompleteSound = '森林鸟鸣',
    this.soundVolume = 0.7,
    this.enableVibration = true,
    this.enableDarkMode = false,
    this.language = 'zh_CN',
    this.showSeconds = true,
    this.enableNotifications = true,
    this.dailyFocusGoalMinutes = 180, // 3小时
    this.weeklyFocusGoalMinutes = 900, // 15小时
    this.enableGoalReminders = true,
  });

  // 获取可选的专注时长选项
  static List<int> get focusDurationOptions => [60, 75, 90, 120];

  // 获取可选的提示音主题
  static List<String> get soundThemeNames => ['自然音', '合成音', 'ASMR'];

  // 获取自然音提示音选项
  static Map<String, List<String>> get soundOptions => {
    '自然音': ['水滴声', '风铃声', '森林鸟鸣', '海浪声', '雨声'],
    '合成音': ['柔和提示音', '清脆提示音', '温和提示音', '简约提示音'],
    'ASMR': ['轻柔耳语', '纸张翻动', '键盘敲击', '笔尖书写'],
  };

  // 获取当前休息间隔（分钟）
  int getBreakInterval() {
    if (useRandomBreakInterval) {
      // 返回随机间隔的中位数用于显示
      return (minBreakIntervalMinutes + maxBreakIntervalMinutes) ~/ 2;
    }
    return fixedBreakIntervalMinutes;
  }

  // 获取当前休息时长（秒）
  int getBreakDuration() {
    if (useRandomBreakDuration) {
      // 返回随机时长的中位数用于显示
      return (minBreakDurationSeconds + maxBreakDurationSeconds) ~/ 2;
    }
    return fixedBreakDurationSeconds;
  }

  // 生成随机休息间隔（分钟）
  int generateRandomBreakInterval() {
    if (!useRandomBreakInterval) return fixedBreakIntervalMinutes;
    
    final random = DateTime.now().millisecondsSinceEpoch % 1000;
    final range = maxBreakIntervalMinutes - minBreakIntervalMinutes;
    return minBreakIntervalMinutes + (random % (range + 1));
  }

  // 生成随机休息时长（秒）
  int generateRandomBreakDuration() {
    if (!useRandomBreakDuration) return fixedBreakDurationSeconds;
    
    final random = DateTime.now().millisecondsSinceEpoch % 1000;
    final range = maxBreakDurationSeconds - minBreakDurationSeconds;
    return minBreakDurationSeconds + (random % (range + 1));
  }

  // 复制设置并修改某些值
  AppSettings copyWith({
    int? focusDurationMinutes,
    bool? useRandomBreakInterval,
    int? fixedBreakIntervalMinutes,
    int? minBreakIntervalMinutes,
    int? maxBreakIntervalMinutes,
    bool? useRandomBreakDuration,
    int? fixedBreakDurationSeconds,
    int? minBreakDurationSeconds,
    int? maxBreakDurationSeconds,
    SoundTheme? soundTheme,
    String? breakStartSound,
    String? breakEndSound,
    String? sessionCompleteSound,
    double? soundVolume,
    bool? enableVibration,
    bool? enableDarkMode,
    String? language,
    bool? showSeconds,
    bool? enableNotifications,
    int? dailyFocusGoalMinutes,
    int? weeklyFocusGoalMinutes,
    bool? enableGoalReminders,
  }) {
    return AppSettings(
      focusDurationMinutes: focusDurationMinutes ?? this.focusDurationMinutes,
      useRandomBreakInterval: useRandomBreakInterval ?? this.useRandomBreakInterval,
      fixedBreakIntervalMinutes: fixedBreakIntervalMinutes ?? this.fixedBreakIntervalMinutes,
      minBreakIntervalMinutes: minBreakIntervalMinutes ?? this.minBreakIntervalMinutes,
      maxBreakIntervalMinutes: maxBreakIntervalMinutes ?? this.maxBreakIntervalMinutes,
      useRandomBreakDuration: useRandomBreakDuration ?? this.useRandomBreakDuration,
      fixedBreakDurationSeconds: fixedBreakDurationSeconds ?? this.fixedBreakDurationSeconds,
      minBreakDurationSeconds: minBreakDurationSeconds ?? this.minBreakDurationSeconds,
      maxBreakDurationSeconds: maxBreakDurationSeconds ?? this.maxBreakDurationSeconds,
      soundTheme: soundTheme ?? this.soundTheme,
      breakStartSound: breakStartSound ?? this.breakStartSound,
      breakEndSound: breakEndSound ?? this.breakEndSound,
      sessionCompleteSound: sessionCompleteSound ?? this.sessionCompleteSound,
      soundVolume: soundVolume ?? this.soundVolume,
      enableVibration: enableVibration ?? this.enableVibration,
      enableDarkMode: enableDarkMode ?? this.enableDarkMode,
      language: language ?? this.language,
      showSeconds: showSeconds ?? this.showSeconds,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      dailyFocusGoalMinutes: dailyFocusGoalMinutes ?? this.dailyFocusGoalMinutes,
      weeklyFocusGoalMinutes: weeklyFocusGoalMinutes ?? this.weeklyFocusGoalMinutes,
      enableGoalReminders: enableGoalReminders ?? this.enableGoalReminders,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'focusDurationMinutes': focusDurationMinutes,
      'useRandomBreakInterval': useRandomBreakInterval,
      'fixedBreakIntervalMinutes': fixedBreakIntervalMinutes,
      'minBreakIntervalMinutes': minBreakIntervalMinutes,
      'maxBreakIntervalMinutes': maxBreakIntervalMinutes,
      'useRandomBreakDuration': useRandomBreakDuration,
      'fixedBreakDurationSeconds': fixedBreakDurationSeconds,
      'minBreakDurationSeconds': minBreakDurationSeconds,
      'maxBreakDurationSeconds': maxBreakDurationSeconds,
      'soundTheme': soundTheme.index,
      'breakStartSound': breakStartSound,
      'breakEndSound': breakEndSound,
      'sessionCompleteSound': sessionCompleteSound,
      'soundVolume': soundVolume,
      'enableVibration': enableVibration,
      'enableDarkMode': enableDarkMode,
      'language': language,
      'showSeconds': showSeconds,
      'enableNotifications': enableNotifications,
      'dailyFocusGoalMinutes': dailyFocusGoalMinutes,
      'weeklyFocusGoalMinutes': weeklyFocusGoalMinutes,
      'enableGoalReminders': enableGoalReminders,
    };
  }

  // 从JSON创建
  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      focusDurationMinutes: json['focusDurationMinutes'] ?? AppConstants.defaultFocusDurationMinutes,
      useRandomBreakInterval: json['useRandomBreakInterval'] ?? true,
      fixedBreakIntervalMinutes: json['fixedBreakIntervalMinutes'] ?? 4,
      minBreakIntervalMinutes: json['minBreakIntervalMinutes'] ?? AppConstants.minBreakIntervalMinutes,
      maxBreakIntervalMinutes: json['maxBreakIntervalMinutes'] ?? AppConstants.maxBreakIntervalMinutes,
      useRandomBreakDuration: json['useRandomBreakDuration'] ?? true,
      fixedBreakDurationSeconds: json['fixedBreakDurationSeconds'] ?? 8,
      minBreakDurationSeconds: json['minBreakDurationSeconds'] ?? AppConstants.minBreakDurationSeconds,
      maxBreakDurationSeconds: json['maxBreakDurationSeconds'] ?? AppConstants.maxBreakDurationSeconds,
      soundTheme: SoundTheme.values[json['soundTheme'] ?? 0],
      breakStartSound: json['breakStartSound'] ?? '水滴声',
      breakEndSound: json['breakEndSound'] ?? '风铃声',
      sessionCompleteSound: json['sessionCompleteSound'] ?? '森林鸟鸣',
      soundVolume: json['soundVolume'] ?? 0.7,
      enableVibration: json['enableVibration'] ?? true,
      enableDarkMode: json['enableDarkMode'] ?? false,
      language: json['language'] ?? 'zh_CN',
      showSeconds: json['showSeconds'] ?? true,
      enableNotifications: json['enableNotifications'] ?? true,
      dailyFocusGoalMinutes: json['dailyFocusGoalMinutes'] ?? 180,
      weeklyFocusGoalMinutes: json['weeklyFocusGoalMinutes'] ?? 900,
      enableGoalReminders: json['enableGoalReminders'] ?? true,
    );
  }
}
