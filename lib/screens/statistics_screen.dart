import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../models/statistics.dart';

// 统计界面
class StatisticsScreen extends StatefulWidget {
  final StatisticsData statisticsData;

  const StatisticsScreen({
    super.key,
    required this.statisticsData,
  });

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(AppConstants.backgroundColor),
      body: Safe<PERSON><PERSON>(
        child: Column(
          children: [
            // 标题栏
            _buildHeader(),
            
            // 统计内容
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.paddingLarge),
                child: Column(
                  children: [
                    _buildTimeStatisticsCard(),
                    const SizedBox(height: AppConstants.paddingLarge),
                    _buildQualityAnalysisCard(),
                    const SizedBox(height: AppConstants.paddingLarge),
                    _buildAchievementsCard(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '统计',
            style: TextStyle(
              fontSize: AppConstants.titleFontSize,
              fontWeight: FontWeight.bold,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeStatisticsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: const Color(AppConstants.cardColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '专注时间统计',
            style: TextStyle(
              fontSize: AppConstants.subtitleFontSize,
              fontWeight: FontWeight.bold,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // 时间统计数据
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildTimeStatItem(
                '今日',
                _formatDuration(widget.statisticsData.getTodayFocusMinutes()),
                Icons.today,
              ),
              _buildTimeStatItem(
                '本周',
                _formatDuration(widget.statisticsData.getWeekFocusMinutes()),
                Icons.calendar_view_week,
              ),
              _buildTimeStatItem(
                '本月',
                _formatDuration(widget.statisticsData.getMonthFocusMinutes()),
                Icons.calendar_month,
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // 图表显示区域
          _buildWeeklyChart(),
        ],
      ),
    );
  }

  Widget _buildQualityAnalysisCard() {
    final completionRate = widget.statisticsData.getCompletionRate();
    final avgBreakInterval = widget.statisticsData.getAverageBreakInterval();
    final avgReactionTime = widget.statisticsData.getAverageReactionTime();
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: const Color(AppConstants.cardColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '专注质量分析',
            style: TextStyle(
              fontSize: AppConstants.subtitleFontSize,
              fontWeight: FontWeight.bold,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          _buildQualityItem('专注完成率', '${(completionRate * 100).toInt()}%'),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildQualityItem('平均小休间隔', '${avgBreakInterval.toStringAsFixed(1)}分钟'),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildQualityItem('平均小休反应时间', '${avgReactionTime.toStringAsFixed(1)}秒'),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // 环形进度图
          _buildCompletionRateProgress(completionRate),
        ],
      ),
    );
  }

  Widget _buildAchievementsCard() {
    final achievements = widget.statisticsData.achievements;
    final unlockedAchievements = achievements.values.toList();
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: const Color(AppConstants.cardColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '成就系统',
            style: TextStyle(
              fontSize: AppConstants.subtitleFontSize,
              fontWeight: FontWeight.bold,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // 最新获得成就
          if (unlockedAchievements.isNotEmpty) ...[
            Text(
              '最新获得:',
              style: TextStyle(
                fontSize: AppConstants.bodyFontSize,
                fontWeight: FontWeight.w600,
                color: const Color(AppConstants.textPrimaryColor),
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            _buildAchievementItem(unlockedAchievements.last, true),
            const SizedBox(height: AppConstants.paddingLarge),
          ],
          
          // 我的全部成就
          Text(
            '我的全部成就:',
            style: TextStyle(
              fontSize: AppConstants.bodyFontSize,
              fontWeight: FontWeight.w600,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          
          if (unlockedAchievements.isEmpty)
            Text(
              '暂无成就，开始专注来解锁吧！',
              style: TextStyle(
                fontSize: AppConstants.captionFontSize,
                color: const Color(AppConstants.textSecondaryColor),
              ),
            )
          else
            ...unlockedAchievements.map((achievement) => 
              Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
                child: _buildAchievementItem(achievement, true),
              ),
            ),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // 未获得成就
          Text(
            '未获得成就:',
            style: TextStyle(
              fontSize: AppConstants.bodyFontSize,
              fontWeight: FontWeight.w600,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          
          _buildPendingAchievements(),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // 心智积分
          _buildMindPointsSection(),
        ],
      ),
    );
  }

  Widget _buildTimeStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(AppConstants.primaryColor),
          size: 24,
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          label,
          style: TextStyle(
            fontSize: AppConstants.captionFontSize,
            color: const Color(AppConstants.textSecondaryColor),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: AppConstants.bodyFontSize,
            fontWeight: FontWeight.bold,
            color: const Color(AppConstants.textPrimaryColor),
          ),
        ),
      ],
    );
  }

  Widget _buildWeeklyChart() {
    final weeklyData = widget.statisticsData.getWeeklyFocusData();
    final maxMinutes = weeklyData.isEmpty ? 1 : 
        weeklyData.map((d) => d.focusMinutes).reduce((a, b) => a > b ? a : b);
    
    return Container(
      height: 100,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: const Color(AppConstants.backgroundColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: weeklyData.map((data) {
          final height = maxMinutes > 0 ? (data.focusMinutes / maxMinutes) * 60 : 0.0;
          return Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                width: 20,
                height: height,
                decoration: BoxDecoration(
                  color: const Color(AppConstants.primaryColor),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _getWeekdayName(data.date.weekday),
                style: TextStyle(
                  fontSize: 10,
                  color: const Color(AppConstants.textSecondaryColor),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildQualityItem(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '$label:',
          style: TextStyle(
            fontSize: AppConstants.bodyFontSize,
            color: const Color(AppConstants.textPrimaryColor),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: AppConstants.bodyFontSize,
            fontWeight: FontWeight.bold,
            color: const Color(AppConstants.primaryColor),
          ),
        ),
      ],
    );
  }

  Widget _buildCompletionRateProgress(double rate) {
    return Column(
      children: [
        Text(
          '本周目标完成度: ${(rate * 100).toInt()}%',
          style: TextStyle(
            fontSize: AppConstants.captionFontSize,
            color: const Color(AppConstants.textSecondaryColor),
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        LinearProgressIndicator(
          value: rate,
          backgroundColor: Colors.grey.withOpacity(0.3),
          valueColor: const AlwaysStoppedAnimation<Color>(
            Color(AppConstants.primaryColor),
          ),
        ),
      ],
    );
  }

  Widget _buildAchievementItem(Achievement achievement, bool isUnlocked) {
    return Row(
      children: [
        Icon(
          isUnlocked ? Icons.check_box : Icons.check_box_outline_blank,
          color: isUnlocked 
              ? const Color(AppConstants.successColor)
              : const Color(AppConstants.textSecondaryColor),
          size: 20,
        ),
        const SizedBox(width: AppConstants.paddingSmall),
        Expanded(
          child: Text(
            '${achievement.name} - ${achievement.description}',
            style: TextStyle(
              fontSize: AppConstants.captionFontSize,
              color: isUnlocked 
                  ? const Color(AppConstants.textPrimaryColor)
                  : const Color(AppConstants.textSecondaryColor),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPendingAchievements() {
    // 这里可以添加未获得的成就列表
    final pendingAchievements = [
      Achievement(
        id: 'master',
        name: '专注大师',
        description: '完成100次小休',
        type: AchievementType.totalBreaks,
        unlockedAt: DateTime.now(),
      ),
      Achievement(
        id: 'expert',
        name: '专注能手',
        description: '完成率达到85%',
        type: AchievementType.completionRate,
        unlockedAt: DateTime.now(),
      ),
    ];

    return Column(
      children: pendingAchievements.map((achievement) => 
        Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
          child: _buildAchievementItem(achievement, false),
        ),
      ).toList(),
    );
  }

  Widget _buildMindPointsSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: const Color(AppConstants.backgroundColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '心智积分:',
                style: TextStyle(
                  fontSize: AppConstants.bodyFontSize,
                  fontWeight: FontWeight.w600,
                  color: const Color(AppConstants.textPrimaryColor),
                ),
              ),
              Text(
                '${widget.statisticsData.mindPoints}',
                style: TextStyle(
                  fontSize: AppConstants.bodyFontSize,
                  fontWeight: FontWeight.bold,
                  color: const Color(AppConstants.primaryColor),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            '解锁提示音包 需要: 500积分',
            style: TextStyle(
              fontSize: AppConstants.captionFontSize,
              color: const Color(AppConstants.textSecondaryColor),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}分钟';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}小时';
      } else {
        return '${hours}小时${remainingMinutes}分钟';
      }
    }
  }

  String _getWeekdayName(int weekday) {
    const weekdays = ['一', '二', '三', '四', '五', '六', '日'];
    return weekdays[weekday - 1];
  }
}
