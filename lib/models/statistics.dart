import '../utils/constants.dart';
import 'focus_session.dart';

// 统计数据模型
class StatisticsData {
  final List<FocusSession> sessions;
  final Map<String, Achievement> achievements;
  final int mindPoints;

  StatisticsData({
    List<FocusSession>? sessions,
    Map<String, Achievement>? achievements,
    this.mindPoints = 0,
  }) : sessions = sessions ?? [],
       achievements = achievements ?? {};

  // 获取今日专注时长（分钟）
  int getTodayFocusMinutes() {
    final today = DateTime.now();
    final todaySessions = sessions.where((session) =>
        session.startTime.year == today.year &&
        session.startTime.month == today.month &&
        session.startTime.day == today.day &&
        session.isCompleted
    );
    
    return todaySessions.fold(0, (sum, session) => sum + session.elapsedMinutes);
  }

  // 获取本周专注时长（分钟）
  int getWeekFocusMinutes() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekStartDate = DateTime(weekStart.year, weekStart.month, weekStart.day);
    
    final weekSessions = sessions.where((session) =>
        session.startTime.isAfter(weekStartDate) && session.isCompleted
    );
    
    return weekSessions.fold(0, (sum, session) => sum + session.elapsedMinutes);
  }

  // 获取本月专注时长（分钟）
  int getMonthFocusMinutes() {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);
    
    final monthSessions = sessions.where((session) =>
        session.startTime.isAfter(monthStart) && session.isCompleted
    );
    
    return monthSessions.fold(0, (sum, session) => sum + session.elapsedMinutes);
  }

  // 获取专注完成率
  double getCompletionRate() {
    if (sessions.isEmpty) return 0.0;
    
    final completedSessions = sessions.where((session) => session.isCompleted).length;
    return completedSessions / sessions.length;
  }

  // 获取平均小休间隔（分钟）
  double getAverageBreakInterval() {
    final completedSessions = sessions.where((session) => session.isCompleted);
    if (completedSessions.isEmpty) return 0.0;
    
    final totalInterval = completedSessions.fold(0.0, (sum, session) => 
        sum + session.averageBreakInterval);
    
    return totalInterval / completedSessions.length;
  }

  // 获取平均小休反应时间（秒）
  double getAverageReactionTime() {
    final allBreaks = sessions.expand((session) => session.breaks);
    if (allBreaks.isEmpty) return 0.0;
    
    final totalReactionTime = allBreaks.fold(0.0, (sum, breakRecord) => 
        sum + breakRecord.reactionTimeSeconds);
    
    return totalReactionTime / allBreaks.length;
  }

  // 获取总小休次数
  int getTotalBreaks() {
    return sessions.fold(0, (sum, session) => sum + session.totalBreaks);
  }

  // 获取连续专注天数
  int getStreakDays() {
    if (sessions.isEmpty) return 0;
    
    final sortedSessions = List<FocusSession>.from(sessions)
      ..sort((a, b) => b.startTime.compareTo(a.startTime));
    
    int streak = 0;
    DateTime? lastDate;
    
    for (final session in sortedSessions) {
      if (!session.isCompleted) continue;
      
      final sessionDate = DateTime(
        session.startTime.year,
        session.startTime.month,
        session.startTime.day,
      );
      
      if (lastDate == null) {
        lastDate = sessionDate;
        streak = 1;
      } else {
        final daysDiff = lastDate.difference(sessionDate).inDays;
        if (daysDiff == 1) {
          streak++;
          lastDate = sessionDate;
        } else {
          break;
        }
      }
    }
    
    return streak;
  }

  // 获取每日专注时长数据（最近7天）
  List<DailyFocusData> getWeeklyFocusData() {
    final now = DateTime.now();
    final weekData = <DailyFocusData>[];
    
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));
      
      final daySessions = sessions.where((session) =>
          session.startTime.isAfter(dayStart) &&
          session.startTime.isBefore(dayEnd) &&
          session.isCompleted
      );
      
      final totalMinutes = daySessions.fold(0, (sum, session) => sum + session.elapsedMinutes);
      
      weekData.add(DailyFocusData(
        date: dayStart,
        focusMinutes: totalMinutes,
        sessionCount: daySessions.length,
      ));
    }
    
    return weekData;
  }

  // 添加专注会话
  void addSession(FocusSession session) {
    sessions.add(session);
    _updateAchievements();
  }

  // 更新成就
  void _updateAchievements() {
    // 首次完成成就
    if (!achievements.containsKey('first_session')) {
      final completedSessions = sessions.where((s) => s.isCompleted);
      if (completedSessions.isNotEmpty) {
        achievements['first_session'] = Achievement(
          id: 'first_session',
          name: '初学者',
          description: '完成首个90分钟专注',
          type: AchievementType.firstSession,
          unlockedAt: DateTime.now(),
        );
      }
    }

    // 连续天数成就
    final streakDays = getStreakDays();
    if (streakDays >= 3 && !achievements.containsKey('three_day_streak')) {
      achievements['three_day_streak'] = Achievement(
        id: 'three_day_streak',
        name: '坚持者',
        description: '连续专注3天',
        type: AchievementType.streak,
        unlockedAt: DateTime.now(),
      );
    }

    // 完成率成就
    final completionRate = getCompletionRate();
    if (completionRate >= AppConstants.mediumCompletionRate && 
        !achievements.containsKey('medium_completion')) {
      achievements['medium_completion'] = Achievement(
        id: 'medium_completion',
        name: '专注新手',
        description: '完成率达到70%',
        type: AchievementType.completionRate,
        unlockedAt: DateTime.now(),
      );
    }

    if (completionRate >= AppConstants.highCompletionRate && 
        !achievements.containsKey('high_completion')) {
      achievements['high_completion'] = Achievement(
        id: 'high_completion',
        name: '专注能手',
        description: '完成率达到85%',
        type: AchievementType.completionRate,
        unlockedAt: DateTime.now(),
      );
    }

    // 总休息次数成就
    final totalBreaks = getTotalBreaks();
    if (totalBreaks >= AppConstants.hundredBreaksAchievement && 
        !achievements.containsKey('hundred_breaks')) {
      achievements['hundred_breaks'] = Achievement(
        id: 'hundred_breaks',
        name: '专注大师',
        description: '完成100次小休',
        type: AchievementType.totalBreaks,
        unlockedAt: DateTime.now(),
      );
    }
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'sessions': sessions.map((s) => s.toJson()).toList(),
      'achievements': achievements.map((key, value) => MapEntry(key, value.toJson())),
      'mindPoints': mindPoints,
    };
  }

  // 从JSON创建
  factory StatisticsData.fromJson(Map<String, dynamic> json) {
    final sessions = (json['sessions'] as List? ?? [])
        .map((s) => FocusSession.fromJson(s))
        .toList();
    
    final achievements = <String, Achievement>{};
    final achievementsJson = json['achievements'] as Map<String, dynamic>? ?? {};
    achievementsJson.forEach((key, value) {
      achievements[key] = Achievement.fromJson(value);
    });
    
    return StatisticsData(
      sessions: sessions,
      achievements: achievements,
      mindPoints: json['mindPoints'] ?? 0,
    );
  }
}

// 每日专注数据
class DailyFocusData {
  final DateTime date;
  final int focusMinutes;
  final int sessionCount;

  DailyFocusData({
    required this.date,
    required this.focusMinutes,
    required this.sessionCount,
  });
}

// 成就模型
class Achievement {
  final String id;
  final String name;
  final String description;
  final AchievementType type;
  final DateTime unlockedAt;

  Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.unlockedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.index,
      'unlockedAt': unlockedAt.toIso8601String(),
    };
  }

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: AchievementType.values[json['type']],
      unlockedAt: DateTime.parse(json['unlockedAt']),
    );
  }
}
